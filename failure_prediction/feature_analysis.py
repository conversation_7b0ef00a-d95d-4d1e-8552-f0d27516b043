#!/usr/bin/env python3
"""
特征分析脚本
分析5个字段的有效性，检查预测结果全部相同的原因
"""

import sys
import os
import gzip
import json
import numpy as np
import pandas as pd
from collections import defaultdict, Counter
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rich.console import Console
from rich.table import Table

console = Console()

def load_data():
    """加载数据"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    data_path = os.path.join(project_root, "failure_prediction", "processed", "processed_new_data_20250927_214625.json.gz")
    
    console.print(f"[cyan]📖 加载数据: {data_path}[/cyan]")
    
    with gzip.open(data_path, "rt", encoding="utf-8") as f:
        data = json.load(f)
    
    console.print(f"✅ 数据加载完成，共 {len(data):,} 个样本")
    return data

def analyze_data_structure(data, sample_size=100):
    """分析数据结构"""
    console.print(f"\n[bold blue]🔍 数据结构分析[/bold blue]")
    
    # 随机采样分析
    sample_data = data[:sample_size]
    
    # 分析feature_list结构
    feature_lengths = []
    field_counts = defaultdict(int)
    field_types = defaultdict(set)
    
    for item in sample_data:
        feature_list = item.get("feature_list", [])
        feature_lengths.append(len(feature_list))
        
        for feature in feature_list[:5]:  # 只看前5个特征
            if len(feature) >= 5:
                for i, field in enumerate(feature[:5]):
                    field_counts[f"field_{i}"] += 1
                    field_types[f"field_{i}"].add(type(field).__name__)
    
    console.print(f"📊 特征序列长度统计:")
    console.print(f"   - 平均长度: {np.mean(feature_lengths):.1f}")
    console.print(f"   - 最小长度: {min(feature_lengths)}")
    console.print(f"   - 最大长度: {max(feature_lengths)}")
    
    console.print(f"\n📋 字段类型分析:")
    for field, types in field_types.items():
        console.print(f"   - {field}: {', '.join(types)}")
    
    return field_types

def analyze_field_values(data, sample_size=1000):
    """分析各字段的值分布"""
    console.print(f"\n[bold blue]🔍 字段值分布分析[/bold blue]")
    
    sample_data = data[:sample_size]
    field_values = defaultdict(list)
    
    for item in sample_data:
        feature_list = item.get("feature_list", [])
        
        for feature in feature_list[:10]:  # 只看前10个特征
            if len(feature) >= 5:
                for i, field in enumerate(feature[:5]):
                    field_values[f"field_{i}"].append(str(field))
    
    # 分析每个字段的唯一值数量和分布
    for field_name, values in field_values.items():
        unique_values = set(values)
        value_counts = Counter(values)
        
        console.print(f"\n📈 {field_name} 分析:")
        console.print(f"   - 总值数: {len(values)}")
        console.print(f"   - 唯一值数: {len(unique_values)}")
        console.print(f"   - 唯一值比例: {len(unique_values)/len(values):.3f}")
        
        # 显示最常见的值
        most_common = value_counts.most_common(5)
        console.print(f"   - 最常见值:")
        for value, count in most_common:
            console.print(f"     {value}: {count} ({count/len(values):.3f})")

def analyze_label_distribution(data):
    """分析标签分布"""
    console.print(f"\n[bold blue]🎯 标签分布分析[/bold blue]")
    
    labels = []
    original_labels = []
    
    for item in data:
        label_list = item.get("label_list", [])
        if label_list:
            original_label = label_list[0]
            original_labels.append(original_label)
            
            # 转换标签（与模型中的逻辑一致）
            new_label = 1 if original_label <= 4 else 0
            labels.append(new_label)
    
    # 原始标签分布
    original_counter = Counter(original_labels)
    console.print(f"📊 原始标签分布:")
    for label, count in sorted(original_counter.items()):
        console.print(f"   - {label}: {count} ({count/len(original_labels):.4f})")
    
    # 转换后标签分布
    label_counter = Counter(labels)
    console.print(f"\n📊 转换后标签分布:")
    for label, count in sorted(label_counter.items()):
        console.print(f"   - {label}: {count} ({count/len(labels):.4f})")
    
    return labels, original_labels

def analyze_feature_label_correlation(data, sample_size=5000):
    """分析特征与标签的相关性"""
    console.print(f"\n[bold blue]🔗 特征-标签相关性分析[/bold blue]")
    
    sample_data = data[:sample_size]
    
    # 收集特征和标签
    features_by_field = defaultdict(list)
    labels = []
    
    for item in sample_data:
        feature_list = item.get("feature_list", [])
        label_list = item.get("label_list", [])
        
        if not label_list:
            continue
            
        label = 1 if label_list[0] <= 4 else 0
        labels.append(label)
        
        # 提取每个字段的特征
        field_features = defaultdict(list)
        
        for feature in feature_list[:20]:  # 看前20个特征
            if len(feature) >= 5:
                for i, field in enumerate(feature[:5]):
                    field_features[f"field_{i}"].append(str(field))
        
        # 计算每个字段的统计特征
        for field_name in [f"field_{i}" for i in range(5)]:
            field_vals = field_features[field_name]
            if field_vals:
                # 唯一值数量
                unique_count = len(set(field_vals))
                # 最常见值的频率
                most_common_freq = Counter(field_vals).most_common(1)[0][1] / len(field_vals)
                
                features_by_field[f"{field_name}_unique_count"].append(unique_count)
                features_by_field[f"{field_name}_most_common_freq"].append(most_common_freq)
            else:
                features_by_field[f"{field_name}_unique_count"].append(0)
                features_by_field[f"{field_name}_most_common_freq"].append(1.0)
    
    # 计算相关性
    labels = np.array(labels)
    
    console.print(f"📈 特征与标签相关性:")
    correlations = {}
    
    for feature_name, feature_values in features_by_field.items():
        if len(feature_values) == len(labels):
            feature_array = np.array(feature_values)
            correlation = np.corrcoef(feature_array, labels)[0, 1]
            correlations[feature_name] = correlation
            
            if abs(correlation) > 0.01:  # 只显示有一定相关性的特征
                console.print(f"   - {feature_name}: {correlation:.4f}")
    
    return correlations

def analyze_sequence_patterns(data, sample_size=1000):
    """分析序列模式"""
    console.print(f"\n[bold blue]🔄 序列模式分析[/bold blue]")
    
    sample_data = data[:sample_size]
    
    positive_sequences = []
    negative_sequences = []
    
    for item in sample_data:
        feature_list = item.get("feature_list", [])
        label_list = item.get("label_list", [])
        
        if not label_list:
            continue
            
        label = 1 if label_list[0] <= 4 else 0
        
        # 提取序列特征
        sequence_features = []
        for feature in feature_list[:10]:
            if len(feature) >= 5:
                # 简单的数值化处理
                numeric_features = []
                for field in feature[1:5]:  # 跳过时间戳，取4个字段
                    try:
                        # 尝试转换为数值
                        if isinstance(field, (int, float)):
                            numeric_features.append(float(field))
                        else:
                            # 字符串哈希
                            numeric_features.append(hash(str(field)) % 1000 / 1000.0)
                    except:
                        numeric_features.append(0.0)
                
                sequence_features.append(numeric_features)
        
        if sequence_features:
            if label == 1:
                positive_sequences.append(sequence_features)
            else:
                negative_sequences.append(sequence_features)
    
    console.print(f"📊 序列统计:")
    console.print(f"   - 正样本序列: {len(positive_sequences)}")
    console.print(f"   - 负样本序列: {len(negative_sequences)}")
    
    # 计算平均序列长度
    if positive_sequences:
        pos_lengths = [len(seq) for seq in positive_sequences]
        console.print(f"   - 正样本平均长度: {np.mean(pos_lengths):.1f}")
    
    if negative_sequences:
        neg_lengths = [len(seq) for seq in negative_sequences]
        console.print(f"   - 负样本平均长度: {np.mean(neg_lengths):.1f}")
    
    return positive_sequences, negative_sequences

def main():
    """主分析函数"""
    console.print("[bold green]🎯 故障预测特征分析[/bold green]")
    console.print("=" * 60)
    
    # 加载数据
    data = load_data()
    
    # 数据结构分析
    field_types = analyze_data_structure(data)
    
    # 字段值分布分析
    analyze_field_values(data)
    
    # 标签分布分析
    labels, original_labels = analyze_label_distribution(data)
    
    # 特征-标签相关性分析
    correlations = analyze_feature_label_correlation(data)
    
    # 序列模式分析
    positive_seqs, negative_seqs = analyze_sequence_patterns(data)
    
    # 总结
    console.print(f"\n[bold yellow]📋 分析总结[/bold yellow]")
    console.print("=" * 60)
    
    # 检查类别不平衡
    pos_ratio = sum(labels) / len(labels)
    console.print(f"🎯 类别平衡:")
    console.print(f"   - 正样本比例: {pos_ratio:.4f}")
    console.print(f"   - 负样本比例: {1-pos_ratio:.4f}")
    console.print(f"   - 不平衡比例: {(1-pos_ratio)/pos_ratio:.1f}:1")
    
    # 特征有效性
    console.print(f"\n🔍 特征有效性:")
    high_corr_features = [name for name, corr in correlations.items() if abs(corr) > 0.05]
    console.print(f"   - 高相关性特征数: {len(high_corr_features)}")
    console.print(f"   - 最高相关性: {max(abs(corr) for corr in correlations.values()):.4f}")
    
    # 可能的问题
    console.print(f"\n⚠️  可能的问题:")
    if pos_ratio < 0.01:
        console.print(f"   - 严重类别不平衡（正样本 < 1%）")
    if len(high_corr_features) == 0:
        console.print(f"   - 特征与标签相关性极低")
    if max(abs(corr) for corr in correlations.values()) < 0.02:
        console.print(f"   - 所有特征的预测能力都很弱")
    
    # 建议
    console.print(f"\n💡 改进建议:")
    console.print(f"   1. 重新设计特征工程，提取更有意义的特征")
    console.print(f"   2. 使用Token-level embedding处理分类特征")
    console.print(f"   3. 考虑时间序列特征和时间编码")
    console.print(f"   4. 增强数据平衡策略")
    console.print(f"   5. 分析特征字段的业务含义")

if __name__ == "__main__":
    main()
