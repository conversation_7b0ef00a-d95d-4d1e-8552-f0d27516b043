"""
故障预测模型 - 简化版本
基于序列处理的统一架构，使用平均池化 + MLP分类器
"""

import os
import gzip
import json
import numpy as np
from collections import defaultdict
import random
from datetime import datetime, timedelta

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint, Callback

from torchmetrics.classification import (
    BinaryAccuracy,
    BinaryPrecision,
    BinaryRecall,
    BinaryF1Score,
    BinaryConfusionMatrix,
    BinaryAUROC,
)

from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE, RandomOverSampler
from sklearn.utils.class_weight import compute_class_weight

# SwanLab 实验跟踪
try:
    import swanlab
    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False
    print("⚠️  SwanLab不可用，将使用本地日志")

from rich.console import Console

console = Console()


class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡"""

    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        bce_loss = nn.functional.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)

        # 计算alpha权重
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)

        # 计算focal权重
        focal_weight = alpha_t * (1 - pt) ** self.gamma

        focal_loss = focal_weight * bce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class LabelSmoothingBCELoss(nn.Module):
    """带标签平滑的二分类交叉熵损失"""

    def __init__(self, smoothing=0.1):
        super(LabelSmoothingBCELoss, self).__init__()
        self.smoothing = smoothing

    def forward(self, inputs, targets):
        # 标签平滑
        targets = targets * (1 - self.smoothing) + 0.5 * self.smoothing
        return nn.functional.binary_cross_entropy_with_logits(inputs, targets)


class PositionalEncoding(nn.Module):
    """位置编码用于Transformer"""

    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class FailurePredictionConfig:
    """故障预测配置类 - 统一使用序列处理模式"""

    def __init__(self):
        """初始化配置参数"""
        # 使用os包管理相对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)  # 获取项目根目录

        # 构建相对路径
        self.data_path = os.path.join(
            project_root,
            "failure_prediction",
            "processed",
            "processed_new_data_20250927_214625.json.gz",
        )
        self.output_dir = os.path.join(project_root, "failure_prediction", "models")
        self.logs_dir = os.path.join(project_root, "failure_prediction", "logs")

        # 确保路径存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)

        # 验证数据文件是否存在
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")

        self.random_seed = 42

        # 数据参数
        self.val_size = 0.15
        self.train_size = 0.7
        self.test_size = 1 - self.train_size - self.val_size

        # Token-level embedding 参数
        self.max_seq_length = 100  # 最大序列长度
        self.min_seq_length = 2  # 最小序列长度
        self.enable_time_deduplication = False  # 是否启用相邻时间去重（数据已预处理时设为False）

        # 字段embedding配置
        self.use_token_embedding = True  # 使用Token-level embedding
        self.embedding_dim = 64  # 每个token的embedding维度

        # 各字段的词汇表大小（基于分析结果）
        self.field_vocab_sizes = {
            'exception_name': 300,    # field_1: 异常名称，214种+padding
            'exception_type': 100,    # field_2: 异常类型，51种+padding
            'warning_level': 10,      # field_3: 警告级别，5种+padding
            'tag_info': 10,           # field_4: 标签信息，3种+padding
        }

        # 时间编码参数
        self.use_time_positional_encoding = True  # 使用时间作为位置编码
        self.time_encoding_dim = 32  # 时间编码维度
        self.max_time_minutes = 10080  # 最大时间范围（7天，分钟）

        # 时间过滤参数
        self.filter_tp_before_hours = 1.0  # 过滤TP前1小时内的记录（设置为None禁用）
        self.use_prediction_mode = False  # 是否使用预测模式（过滤TP时刻后的记录）

        # 模型架构选择
        self.model_type = "lstm"  # "mlp", "lstm", "gru", "transformer"
        self.hidden_dim = 256  # 增加隐藏维度
        self.num_layers = 3  # LSTM/GRU层数
        self.dropout_rate = 0.4
        self.bidirectional = True  # 双向LSTM/GRU

        # Transformer特定参数
        self.num_heads = 8
        self.dim_feedforward = 512

        # 训练参数
        self.batch_size = 32
        self.learning_rate = 0.001  # 降低学习率
        self.max_epochs = 100
        self.patience = 15  # 增加耐心值
        self.weight_decay = 1e-4

        # 数据平衡策略
        self.use_oversampling = True  # 使用过采样而不是权重
        self.oversampling_method = "random"  # "random", "smote"
        self.oversampling_ratio = 0.3  # 过采样后正样本比例（降低一些避免过拟合）

        # 类别权重（当不使用过采样时）
        self.use_class_weights = False  # 改为False，使用过采样
        self.pos_weight_scale = 2

        # 扩展方法
        self.use_focal_loss = True  # 使用Focal Loss
        self.focal_alpha = 0.25
        self.focal_gamma = 2.0

        self.use_label_smoothing = True  # 标签平滑
        self.label_smoothing = 0.1

        self.use_mixup = False  # Mixup数据增强（对序列数据较复杂）
        self.mixup_alpha = 0.2

        # 学习率调度
        self.use_lr_scheduler = True
        self.lr_scheduler_type = "cosine"  # "cosine", "step", "plateau"
        self.lr_warmup_epochs = 5

        # 设备
        self.accelerator = "gpu" if torch.cuda.is_available() else "cpu"
        self.devices = [0] if torch.cuda.is_available() else "auto"

    def to_dict(self):
        """将配置转换为字典，用于SwanLab记录"""
        config_dict = {}

        # 遍历所有属性
        for attr_name in dir(self):
            if not attr_name.startswith('_'):  # 排除私有属性
                attr_value = getattr(self, attr_name)
                # 只记录基本数据类型
                if isinstance(attr_value, (int, float, str, bool, list, tuple)):
                    config_dict[attr_name] = attr_value
                elif attr_value is None:
                    config_dict[attr_name] = None

        return config_dict

    def update_from_dict(self, config_dict):
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def get_model_info(self):
        """获取模型相关信息用于SwanLab"""
        return {
            "model_architecture": self.model_type,
            "hidden_dim": self.hidden_dim,
            "num_layers": self.num_layers,
            "bidirectional": self.bidirectional if hasattr(self, 'bidirectional') else False,
            "dropout_rate": self.dropout_rate,
            "feature_dim": self.feature_dim,
            "max_seq_length": self.max_seq_length,
        }

    def get_training_info(self):
        """获取训练相关信息用于SwanLab"""
        return {
            "batch_size": self.batch_size,
            "learning_rate": self.learning_rate,
            "max_epochs": self.max_epochs,
            "patience": self.patience,
            "weight_decay": self.weight_decay,
            "optimizer": "AdamW",
            "lr_scheduler": self.lr_scheduler_type if self.use_lr_scheduler else None,
            "lr_warmup_epochs": self.lr_warmup_epochs if self.use_lr_scheduler else 0,
        }

    def get_data_info(self):
        """获取数据相关信息用于SwanLab"""
        return {
            "train_size": self.train_size,
            "val_size": self.val_size,
            "test_size": self.test_size,
            "oversampling": self.use_oversampling,
            "oversampling_method": self.oversampling_method if self.use_oversampling else None,
            "oversampling_ratio": self.oversampling_ratio if self.use_oversampling else None,
            "time_deduplication": self.enable_time_deduplication,
            "filter_tp_before_hours": self.filter_tp_before_hours,
        }

    def get_loss_info(self):
        """获取损失函数相关信息用于SwanLab"""
        loss_type = "focal" if self.use_focal_loss else \
                   "label_smoothing" if self.use_label_smoothing else \
                   "weighted_bce" if self.use_class_weights else "bce"

        return {
            "loss_type": loss_type,
            "focal_alpha": self.focal_alpha if self.use_focal_loss else None,
            "focal_gamma": self.focal_gamma if self.use_focal_loss else None,
            "label_smoothing": self.label_smoothing if self.use_label_smoothing else None,
            "pos_weight_scale": self.pos_weight_scale if self.use_class_weights else None,
        }


class TokenEmbeddingDataset(Dataset):
    """基于Token-level embedding的故障预测数据集"""

    def __init__(self, data, config, vocab_mappings=None):
        self.data = data
        self.config = config
        self.vocab_mappings = vocab_mappings or {}

        # 构建词汇表（仅在训练集上）
        if not self.vocab_mappings:
            self._build_vocabularies()

        # 预处理数据
        self._preprocess_token_data()

    def _build_vocabularies(self):
        """构建各字段的词汇表"""
        console.print("[cyan]🔧 构建Token词汇表...[/cyan]")

        # 收集所有字段的唯一值
        field_values = {
            'exception_name': set(),
            'exception_type': set(),
            'warning_level': set(),
            'tag_info': set(),
        }

        for item in self.data[:10000]:  # 使用前10000个样本构建词汇表
            feature_list = item.get("feature_list", [])

            for feature in feature_list:
                if len(feature) >= 5:
                    # field_1: 异常名称
                    field_values['exception_name'].add(str(feature[1]))
                    # field_2: 异常类型
                    field_values['exception_type'].add(str(feature[2]))
                    # field_3: 警告级别
                    field_values['warning_level'].add(str(feature[3]))
                    # field_4: 标签信息
                    field_values['tag_info'].add(str(feature[4]))

        # 构建词汇表映射（保留0作为padding，1作为unknown）
        self.vocab_mappings = {}
        for field_name, values in field_values.items():
            vocab = {'<PAD>': 0, '<UNK>': 1}
            for i, value in enumerate(sorted(values), 2):
                vocab[value] = i
            self.vocab_mappings[field_name] = vocab

            console.print(f"   - {field_name}: {len(vocab)} tokens")

        console.print(f"✅ 词汇表构建完成")

    def _parse_timestamp(self, timestamp_str, formats=None):
        """解析时间戳（优化版本）"""
        if formats is None:
            formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]

        try:
            # 尝试不同的时间格式
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            # 如果都失败，返回None
            return None
        except:
            return None

    def _preprocess_token_data(self):
        """Token-level数据预处理"""
        token_sequences = []
        time_positions = []
        labels = []
        lengths = []

        # 预编译时间格式以提升解析效率
        time_formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]

        # 批量处理进度显示
        total_items = len(self.data)

        console.print(f"[cyan]开始Token-level数据处理 {total_items:,} 个样本...[/cyan]")

        for item_idx, item in enumerate(self.data):
            # 进度显示（每处理10000个样本显示一次）
            if item_idx % 10000 == 0 and item_idx > 0:
                console.print(f"[cyan]已处理 {item_idx:,}/{total_items:,} 样本，有效样本: {len(sequences):,}[/cyan]")

            # 获取特征列表
            feature_list = item["feature_list"]
            if not feature_list or len(feature_list) < self.config.min_seq_length:
                continue

            try:
                # 优化：预先获取TP时间戳，避免后续无效处理
                tp_timestamp_str = item["group_key"][3]
                tp_timestamp = self._parse_timestamp(str(tp_timestamp_str), time_formats)

                if tp_timestamp is None:
                    # 只抽样输出TP时间戳解析错误（每1000个样本输出一次）
                    if item_idx % 1000 == 0:
                        console.print(
                            f"[yellow]⚠️  无法解析TP时间戳: {tp_timestamp_str}, 跳过此样本[/yellow]"
                        )
                    continue

                # 解析时间戳并排序（优化：使用列表推导式）
                timestamped_features = []
                for i, feature in enumerate(feature_list):
                    if len(feature) > 0:
                        timestamp_str = feature[0]  # 时间戳在第一个位置
                        timestamp = self._parse_timestamp(str(timestamp_str), time_formats)
                        if timestamp:
                            timestamped_features.append((timestamp, feature, i))

                if len(timestamped_features) < self.config.min_seq_length:
                    continue

                # 按时间排序
                timestamped_features.sort(key=lambda x: x[0])

                # 过滤记录
                filtered_features = []
                if (
                    self.config.use_prediction_mode
                    and self.config.filter_tp_before_hours is not None
                ):
                    # 计算过滤时间窗口
                    filter_before_time = tp_timestamp - timedelta(
                        hours=self.config.filter_tp_before_hours
                    )

                    # 只保留在过滤时间之前的记录
                    for timestamp, feature, idx in timestamped_features:
                        if timestamp < filter_before_time:
                            filtered_features.append((timestamp, feature, idx))

                    # 只在异常情况下且抽样输出（过滤掉太多记录）
                    if len(filtered_features) < len(timestamped_features) * 0.5 and len(sequences) % 50 == 0:
                        console.print(
                            f"[yellow]⚠️  过滤掉大量记录: 原始{len(timestamped_features)} -> 过滤后{len(filtered_features)}[/yellow]"
                        )
                else:
                    filtered_features = timestamped_features

                if len(filtered_features) < self.config.min_seq_length:
                    # 只抽样输出严重异常情况（每100个样本输出一次）
                    if len(filtered_features) < self.config.min_seq_length // 2 and len(sequences) % 100 == 0:
                        console.print(
                            f"[yellow]⚠️  过滤后记录数严重不足: {len(filtered_features)}, 跳过此样本[/yellow]"
                        )
                    continue

                # 提取特征和计算相对时间
                base_time = filtered_features[0][0]
                seq_features = []
                time_diffs = []

                # 根据配置决定是否进行时间去重
                if self.config.enable_time_deduplication:
                    # 启用相邻时间去重（原始逻辑）
                    processed_features = []
                    last_timestamp = None

                    for timestamp, feature, idx in filtered_features:
                        # 去重：跳过相同时间戳的记录
                        if last_timestamp is None or timestamp != last_timestamp:
                            processed_features.append((timestamp, feature, idx))
                            last_timestamp = timestamp

                        # 限制最大长度
                        if len(processed_features) >= self.config.max_seq_length:
                            break
                else:
                    # 未启用相邻时间去重，直接使用过滤后的特征
                    processed_features = filtered_features[:self.config.max_seq_length]

                # 优化：批量处理特征
                for timestamp, feature, _ in processed_features:
                    try:
                        # 计算相对时间差（分钟）
                        time_diff = (timestamp - base_time).total_seconds() / 60.0
                        time_diffs.append(time_diff)

                        # 优化：使用numpy进行批量特征处理
                        feature_vals = feature[1:]  # 跳过时间戳

                        # 转换为float数组，无效值设为0
                        processed_vals = []
                        for val in feature_vals:
                            try:
                                if val is None:
                                    processed_vals.append(0.0)
                                else:
                                    float_val = float(val)
                                    processed_vals.append(float_val if np.isfinite(float_val) else 0.0)
                            except (ValueError, TypeError):
                                processed_vals.append(0.0)

                        # 补齐或截断到指定维度
                        if len(processed_vals) < self.config.feature_dim:
                            processed_vals.extend([0.0] * (self.config.feature_dim - len(processed_vals)))
                        else:
                            processed_vals = processed_vals[:self.config.feature_dim]

                        seq_features.append(processed_vals)

                    except Exception as e:
                        # 只在特定情况下输出异常（减少输出量）
                        if item_idx % 1000 == 0 and ("invalid literal" in str(e) or "cannot convert" in str(e)):
                            console.print(f"[yellow]⚠️  特征解析错误: {e}[/yellow]")
                        continue

                if len(seq_features) < self.config.min_seq_length:
                    continue

                # 转换为numpy数组
                seq_features = np.array(seq_features, dtype=np.float32)
                time_diffs = np.array(time_diffs, dtype=np.float32)

                # 特征标准化
                if self.feature_scaler is None:
                    self.feature_scaler = StandardScaler()
                    seq_features = self.feature_scaler.fit_transform(seq_features)
                else:
                    seq_features = self.feature_scaler.transform(seq_features)

                sequences.append(seq_features)
                time_diffs_list.append(time_diffs)
                lengths.append(len(seq_features))

                # 处理标签
                original_label = item["label_list"][0]
                new_label = 1 if original_label <= 4 else 0
                labels.append(new_label)

            except Exception as e:
                # 只在严重错误时输出（减少输出量）
                if "KeyError" in str(e) or "IndexError" in str(e):
                    console.print(f"[yellow]⚠️  序列处理严重错误: {e}[/yellow]")
                continue

        self.sequences = sequences
        self.labels = np.array(labels, dtype=np.int32)
        self.time_diffs = time_diffs_list
        self.lengths = np.array(lengths, dtype=np.int32)

        console.print(f"✅ 处理完成！有效序列: {len(sequences):,}/{total_items:,} ({len(sequences)/total_items*100:.1f}%)")

        # 应用过采样（仅对训练集）
        if self.config.use_oversampling and self.feature_scaler is None:  # 只对训练集过采样
            self._apply_oversampling()

    def _apply_oversampling(self):
        """应用过采样平衡数据集"""
        console.print("[cyan]🔄 应用过采样平衡数据集...[/cyan]")

        # 统计原始类别分布
        unique, counts = np.unique(self.labels, return_counts=True)
        original_dist = dict(zip(unique, counts))
        console.print(f"原始类别分布: {original_dist}")

        # 计算需要的正样本数量
        neg_count = original_dist.get(0, 0)
        pos_count = original_dist.get(1, 0)

        if pos_count == 0:
            console.print("[yellow]⚠️  没有正样本，跳过过采样[/yellow]")
            return

        # 计算目标正样本数量
        target_pos_count = int(neg_count * self.config.oversampling_ratio / (1 - self.config.oversampling_ratio))

        if target_pos_count <= pos_count:
            console.print(f"[yellow]⚠️  正样本数量已足够 ({pos_count} >= {target_pos_count})，跳过过采样[/yellow]")
            return

        # 找到所有正样本的索引
        pos_indices = np.where(self.labels == 1)[0]

        # 随机过采样正样本
        if self.config.oversampling_method == "random":
            # 计算需要复制的数量
            samples_to_add = target_pos_count - pos_count

            # 随机选择正样本进行复制
            np.random.seed(self.config.random_seed)
            indices_to_duplicate = np.random.choice(pos_indices, size=samples_to_add, replace=True)

            # 复制序列、时间差、长度和标签
            for idx in indices_to_duplicate:
                self.sequences.append(self.sequences[idx].copy())
                self.time_diffs.append(self.time_diffs[idx].copy())
                self.lengths = np.append(self.lengths, self.lengths[idx])

            # 添加对应的标签
            new_labels = np.ones(samples_to_add, dtype=np.int32)
            self.labels = np.concatenate([self.labels, new_labels])

        elif self.config.oversampling_method == "smote":
            # 使用SMOTE进行过采样（需要将序列数据展平）
            console.print("[yellow]⚠️  SMOTE过采样对序列数据较复杂，使用随机过采样[/yellow]")
            # 暂时使用随机过采样的逻辑
            samples_to_add = target_pos_count - pos_count
            np.random.seed(self.config.random_seed)
            indices_to_duplicate = np.random.choice(pos_indices, size=samples_to_add, replace=True)

            for idx in indices_to_duplicate:
                self.sequences.append(self.sequences[idx].copy())
                self.time_diffs.append(self.time_diffs[idx].copy())
                self.lengths = np.append(self.lengths, self.lengths[idx])

            new_labels = np.ones(samples_to_add, dtype=np.int32)
            self.labels = np.concatenate([self.labels, new_labels])

        # 统计过采样后的类别分布
        unique, counts = np.unique(self.labels, return_counts=True)
        new_dist = dict(zip(unique, counts))
        console.print(f"过采样后类别分布: {new_dist}")
        console.print(f"✅ 过采样完成！数据集大小: {len(self.sequences):,}")

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        sequence = torch.FloatTensor(self.sequences[idx])
        time_diffs = torch.FloatTensor(self.time_diffs[idx])
        # 修复：确保标签是标量，不是向量
        label = torch.FloatTensor([self.labels[idx]]).squeeze()  # 移除多余维度
        length = torch.LongTensor([self.lengths[idx]])

        return {
            "sequence": sequence,
            "time_diffs": time_diffs,
            "label": label,
            "length": length,
        }


class FailurePredictionModel(pl.LightningModule):
    """增强的故障预测模型 - 支持多种序列架构"""

    def __init__(self, config):
        super(FailurePredictionModel, self).__init__()
        self.save_hyperparameters(config.__dict__)

        self.config = config

        # 构建序列编码器
        self.encoder = self._build_encoder()

        # 计算编码器输出维度
        encoder_output_dim = self._get_encoder_output_dim()

        # 构建分类器
        self.classifier = nn.Sequential(
            nn.Linear(encoder_output_dim, config.hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(config.hidden_dim),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(config.hidden_dim // 2),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim // 2, config.hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim // 4, 1),
        )

        # 损失函数选择
        if config.use_focal_loss:
            self.criterion = FocalLoss(alpha=config.focal_alpha, gamma=config.focal_gamma)
        elif config.use_label_smoothing:
            self.criterion = LabelSmoothingBCELoss(smoothing=config.label_smoothing)
        elif config.use_class_weights:
            pos_weight = torch.tensor([config.pos_weight_scale])
            self.criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        else:
            self.criterion = nn.BCEWithLogitsLoss()

        # 评估指标
        self._setup_metrics()

    def _build_encoder(self):
        """构建序列编码器"""
        if self.config.model_type == "mlp":
            # 简单MLP（平均池化后直接分类）
            return None

        elif self.config.model_type == "lstm":
            return nn.LSTM(
                input_size=self.config.feature_dim,
                hidden_size=self.config.hidden_dim,
                num_layers=self.config.num_layers,
                batch_first=True,
                dropout=self.config.dropout_rate if self.config.num_layers > 1 else 0,
                bidirectional=self.config.bidirectional
            )

        elif self.config.model_type == "gru":
            return nn.GRU(
                input_size=self.config.feature_dim,
                hidden_size=self.config.hidden_dim,
                num_layers=self.config.num_layers,
                batch_first=True,
                dropout=self.config.dropout_rate if self.config.num_layers > 1 else 0,
                bidirectional=self.config.bidirectional
            )

        elif self.config.model_type == "transformer":
            # 位置编码
            self.pos_encoder = PositionalEncoding(self.config.feature_dim, self.config.max_seq_length)

            encoder_layer = nn.TransformerEncoderLayer(
                d_model=self.config.feature_dim,
                nhead=self.config.num_heads,
                dim_feedforward=self.config.dim_feedforward,
                dropout=self.config.dropout_rate,
                batch_first=True,
                activation='gelu'
            )
            return nn.TransformerEncoder(encoder_layer, num_layers=self.config.num_layers)

        else:
            raise ValueError(f"不支持的模型类型: {self.config.model_type}")

    def _get_encoder_output_dim(self):
        """获取编码器输出维度"""
        if self.config.model_type == "mlp":
            return self.config.feature_dim
        elif self.config.model_type in ["lstm", "gru"]:
            return self.config.hidden_dim * (2 if self.config.bidirectional else 1)
        elif self.config.model_type == "transformer":
            return self.config.feature_dim
        else:
            raise ValueError(f"不支持的模型类型: {self.config.model_type}")

    def _setup_metrics(self):
        """设置评估指标"""
        self.train_accuracy = BinaryAccuracy()
        self.train_precision = BinaryPrecision()
        self.train_recall = BinaryRecall()
        self.train_f1 = BinaryF1Score()
        self.train_auc = BinaryAUROC()

        self.val_accuracy = BinaryAccuracy()
        self.val_precision = BinaryPrecision()
        self.val_recall = BinaryRecall()
        self.val_f1 = BinaryF1Score()
        self.val_auc = BinaryAUROC()

        self.test_accuracy = BinaryAccuracy()
        self.test_precision = BinaryPrecision()
        self.test_recall = BinaryRecall()
        self.test_f1 = BinaryF1Score()
        self.test_auc = BinaryAUROC()
        self.test_confusion_matrix = BinaryConfusionMatrix()

    def forward(self, sequences, time_diffs=None, lengths=None):
        """前向传播 - 支持多种序列编码器"""
        batch_size, seq_len, _ = sequences.size()

        if self.config.model_type == "mlp":
            # MLP模式：使用平均池化
            if lengths is not None and lengths.min() > 0:
                lengths = lengths.to(sequences.device)
                mask = torch.arange(seq_len, device=sequences.device).unsqueeze(0) < lengths.unsqueeze(1)
                mask = mask.unsqueeze(-1).float()
                masked_sequences = sequences * mask
                pooled_features = masked_sequences.sum(dim=1) / lengths.unsqueeze(1).float()
            else:
                pooled_features = sequences.mean(dim=1)
            encoded_features = pooled_features

        elif self.config.model_type in ["lstm", "gru"]:
            # LSTM/GRU模式
            if lengths is not None:
                # 使用pack_padded_sequence处理变长序列
                lengths_cpu = lengths.cpu()
                packed_sequences = nn.utils.rnn.pack_padded_sequence(
                    sequences, lengths_cpu, batch_first=True, enforce_sorted=False
                )
                if self.config.model_type == "lstm":
                    packed_output, (hidden, _) = self.encoder(packed_sequences)
                else:  # GRU
                    packed_output, hidden = self.encoder(packed_sequences)

                # 使用最后一个隐藏状态
                if self.config.bidirectional:
                    # 双向LSTM/GRU：连接前向和后向的最后隐藏状态
                    hidden = hidden.view(self.config.num_layers, 2, batch_size, -1)
                    encoded_features = torch.cat([hidden[-1, 0], hidden[-1, 1]], dim=1)
                else:
                    encoded_features = hidden[-1]  # 取最后一层的隐藏状态
            else:
                if self.config.model_type == "lstm":
                    output, (hidden, _) = self.encoder(sequences)
                else:  # GRU
                    output, hidden = self.encoder(sequences)
                if self.config.bidirectional:
                    hidden = hidden.view(self.config.num_layers, 2, batch_size, -1)
                    encoded_features = torch.cat([hidden[-1, 0], hidden[-1, 1]], dim=1)
                else:
                    encoded_features = hidden[-1]

        elif self.config.model_type == "transformer":
            # Transformer模式
            # 添加位置编码
            sequences = self.pos_encoder(sequences.transpose(0, 1)).transpose(0, 1)

            # 创建padding mask
            if lengths is not None:
                lengths = lengths.to(sequences.device)
                mask = torch.arange(seq_len, device=sequences.device).unsqueeze(0) >= lengths.unsqueeze(1)
            else:
                mask = None

            # Transformer编码
            encoded_seq = self.encoder(sequences, src_key_padding_mask=mask)

            # 全局平均池化（忽略padding部分）
            if lengths is not None:
                mask_float = (~mask).float().unsqueeze(-1)
                masked_encoded = encoded_seq * mask_float
                encoded_features = masked_encoded.sum(dim=1) / mask_float.sum(dim=1)
            else:
                encoded_features = encoded_seq.mean(dim=1)

        else:
            raise ValueError(f"不支持的模型类型: {self.config.model_type}")

        # 分类器
        logits = self.classifier(encoded_features)
        return logits.squeeze(-1)

    def training_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        # 修复：确保标签维度正确
        labels = labels.squeeze()  # 确保标签是 [batch_size] 而不是 [batch_size, 1]
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat)
        preds = (probs > 0.5).int()

        # 更新指标
        self.train_accuracy.update(preds, labels.int())
        self.train_precision.update(preds, labels.int())
        self.train_recall.update(preds, labels.int())
        self.train_f1.update(preds, labels.int())
        self.train_auc.update(probs, labels.int())

        self.log("train_loss", loss, prog_bar=True)
        return loss

    def on_train_epoch_end(self):
        # 记录训练指标
        self.log("train_accuracy", self.train_accuracy.compute())
        self.log("train_precision", self.train_precision.compute())
        self.log("train_recall", self.train_recall.compute())
        self.log("train_f1", self.train_f1.compute())
        self.log("train_auc", self.train_auc.compute())

        # 重置指标
        self.train_accuracy.reset()
        self.train_precision.reset()
        self.train_recall.reset()
        self.train_f1.reset()
        self.train_auc.reset()

    def validation_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        # 修复：确保标签维度正确
        labels = labels.squeeze()  # 确保标签是 [batch_size] 而不是 [batch_size, 1]
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat)
        preds = (probs > 0.5).int()

        # 更新指标
        self.val_accuracy.update(preds, labels.int())
        self.val_precision.update(preds, labels.int())
        self.val_recall.update(preds, labels.int())
        self.val_f1.update(preds, labels.int())
        self.val_auc.update(probs, labels.int())

        self.log("val_loss", loss, prog_bar=True)
        return loss

    def on_validation_epoch_end(self):
        # 记录验证指标
        self.log("val_accuracy", self.val_accuracy.compute(), prog_bar=True)
        self.log("val_precision", self.val_precision.compute())
        self.log("val_recall", self.val_recall.compute())
        self.log("val_f1", self.val_f1.compute(), prog_bar=True)
        self.log("val_auc", self.val_auc.compute())

        # 重置指标
        self.val_accuracy.reset()
        self.val_precision.reset()
        self.val_recall.reset()
        self.val_f1.reset()
        self.val_auc.reset()

    def test_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        # 修复：确保标签维度正确
        labels = labels.squeeze()  # 确保标签是 [batch_size] 而不是 [batch_size, 1]
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat)
        preds = (probs > 0.5).int()

        # 更新指标
        self.test_accuracy.update(preds, labels.int())
        self.test_precision.update(preds, labels.int())
        self.test_recall.update(preds, labels.int())
        self.test_f1.update(preds, labels.int())
        self.test_auc.update(probs, labels.int())
        self.test_confusion_matrix.update(preds, labels.int())

        return loss

    def on_test_epoch_end(self):
        # 记录测试指标
        accuracy = self.test_accuracy.compute()
        precision = self.test_precision.compute()
        recall = self.test_recall.compute()
        f1 = self.test_f1.compute()
        auc = self.test_auc.compute()
        conf_matrix = self.test_confusion_matrix.compute()

        self.log("test_accuracy", accuracy)
        self.log("test_precision", precision)
        self.log("test_recall", recall)
        self.log("test_f1", f1)
        self.log("test_auc", auc)

        # 打印详细结果
        console.print("\n[bold green]🧪 测试结果详情:[/bold green]")
        console.print(f"准确率 (Accuracy): {accuracy:.4f}")
        console.print(f"精确率 (Precision): {precision:.4f}")
        console.print(f"召回率 (Recall): {recall:.4f}")
        console.print(f"F1分数 (F1-Score): {f1:.4f}")
        console.print(f"AUC得分: {auc:.4f}")

        console.print("\n[bold blue]混淆矩阵 (Confusion Matrix):[/bold blue]")
        console.print(f"{conf_matrix}")

        # 计算每个类别的详细指标
        tn, fp, fn, tp = conf_matrix.flatten()

        console.print("\n[bold cyan]详细分类指标:[/bold cyan]")
        console.print(f"真阳性 (TP): {tp}")
        console.print(f"假阳性 (FP): {fp}")
        console.print(f"真阴性 (TN): {tn}")
        console.print(f"假阴性 (FN): {fn}")

        if tp + fp > 0:
            console.print(f"阳性预测值 (PPV): {tp / (tp + fp):.4f}")
        if tp + fn > 0:
            console.print(f"真阳性率 (TPR): {tp / (tp + fn):.4f}")
        if tn + fp > 0:
            console.print(f"真阴性率 (TNR): {tn / (tn + fp):.4f}")

    def configure_optimizers(self):
        optimizer = optim.AdamW(  # 使用AdamW优化器
            self.parameters(),
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        if not self.config.use_lr_scheduler:
            return optimizer

        # 学习率调度器
        if self.config.lr_scheduler_type == "cosine":
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.config.max_epochs,
                eta_min=self.config.learning_rate * 0.01
            )
        elif self.config.lr_scheduler_type == "step":
            scheduler = optim.lr_scheduler.StepLR(
                optimizer,
                step_size=self.config.max_epochs // 3,
                gamma=0.1
            )
        elif self.config.lr_scheduler_type == "plateau":
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='max',
                factor=0.5,
                patience=self.config.patience // 2,
                verbose=True
            )
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "monitor": "val_f1",
                    "frequency": 1
                }
            }
        else:
            return optimizer

        # Warmup调度器
        if self.config.lr_warmup_epochs > 0:
            warmup_scheduler = optim.lr_scheduler.LinearLR(
                optimizer,
                start_factor=0.1,
                total_iters=self.config.lr_warmup_epochs
            )
            scheduler = optim.lr_scheduler.SequentialLR(
                optimizer,
                schedulers=[warmup_scheduler, scheduler],
                milestones=[self.config.lr_warmup_epochs]
            )

        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "frequency": 1
            }
        }


class SwanLabCallback(Callback):
    """SwanLab回调"""

    def __init__(self):
        if SWANLAB_AVAILABLE:
            self.run = swanlab.init(
                project="failure_prediction",
                experiment_name=f"lightning_experiment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

    def on_train_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "train_loss": metrics.get("train_loss", 0),
                    "train_accuracy": metrics.get("train_accuracy", 0),
                    "train_precision": metrics.get("train_precision", 0),
                    "train_recall": metrics.get("train_recall", 0),
                    "train_f1": metrics.get("train_f1", 0),
                    "train_auc": metrics.get("train_auc", 0),
                }
            )

    def on_validation_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "val_loss": metrics.get("val_loss", 0),
                    "val_accuracy": metrics.get("val_accuracy", 0),
                    "val_precision": metrics.get("val_precision", 0),
                    "val_recall": metrics.get("val_recall", 0),
                    "val_f1": metrics.get("val_f1", 0),
                    "val_auc": metrics.get("val_auc", 0),
                }
            )

    def on_test_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "test_accuracy": metrics.get("test_accuracy", 0),
                    "test_precision": metrics.get("test_precision", 0),
                    "test_recall": metrics.get("test_recall", 0),
                    "test_f1": metrics.get("test_f1", 0),
                    "test_auc": metrics.get("test_auc", 0),
                }
            )
            swanlab.finish()


def load_and_split_data(config):
    """加载数据并按照TP分组划分"""
    console.print("[bold cyan]📖 加载数据...[/bold cyan]")

    with gzip.open(config.data_path, "rt", encoding="utf-8") as f:
        data = json.load(f)

    console.print(f"✅ 加载了 {len(data)} 个分组数据")

    # 按照TP分组
    tp_groups = defaultdict(list)
    for item in data:
        tp_value = item["group_key"][3]  # TP字段
        tp_groups[tp_value].append(item)

    # 划分TP组
    tp_list = list(tp_groups.keys())
    random.seed(config.random_seed)
    random.shuffle(tp_list)

    train_tp_count = int(len(tp_list) * config.train_size)
    val_tp_count = int(len(tp_list) * config.val_size)

    train_tp = tp_list[:train_tp_count]
    val_tp = tp_list[train_tp_count : train_tp_count + val_tp_count]
    test_tp = tp_list[train_tp_count + val_tp_count :]

    # 收集数据
    train_data = []
    val_data = []
    test_data = []

    for tp in train_tp:
        train_data.extend(tp_groups[tp])
    for tp in val_tp:
        val_data.extend(tp_groups[tp])
    for tp in test_tp:
        test_data.extend(tp_groups[tp])

    console.print("📊 数据划分完成:")
    console.print(f"  训练集: {len(train_data)} 样本")
    console.print(f"  验证集: {len(val_data)} 样本")
    console.print(f"  测试集: {len(test_data)} 样本")

    return train_data, val_data, test_data


def collate_fn(batch):
    """自定义collate函数处理变长序列"""
    sequences = []
    time_diffs = []
    labels = []
    lengths = []

    for item in batch:
        sequences.append(item["sequence"])
        time_diffs.append(item["time_diffs"])
        labels.append(item["label"])
        lengths.append(item["length"])

    # 填充序列到相同长度
    max_len = max(len(seq) for seq in sequences)
    padded_sequences = []
    padded_time_diffs = []

    for seq, time_diff in zip(sequences, time_diffs):
        # 填充序列
        pad_len = max_len - len(seq)
        if pad_len > 0:
            padding = torch.zeros(pad_len, seq.size(-1))
            padded_seq = torch.cat([seq, padding], dim=0)
            time_padding = torch.zeros(pad_len)
            padded_time = torch.cat([time_diff, time_padding], dim=0)
        else:
            padded_seq = seq
            padded_time = time_diff

        padded_sequences.append(padded_seq)
        padded_time_diffs.append(padded_time)

    return {
        "sequence": torch.stack(padded_sequences),
        "time_diffs": torch.stack(padded_time_diffs),
        "label": torch.stack(labels),  # 修复：保持 [batch_size] 维度，不添加额外维度
        "length": torch.cat(lengths),
    }


def create_data_loaders(train_data, val_data, test_data, config):
    """创建数据加载器"""
    console.print("[bold cyan]🔧 创建数据集和数据加载器...[/bold cyan]")

    # 创建训练集（用于特征标准化）
    temp_dataset = FailurePredictionDataset(
        train_data[:10], config
    )  # 小样本用于初始化标准化器
    feature_scaler = temp_dataset.feature_scaler

    # 创建完整数据集
    train_dataset = FailurePredictionDataset(train_data, config, feature_scaler)
    val_dataset = FailurePredictionDataset(val_data, config, feature_scaler)
    test_dataset = FailurePredictionDataset(test_data, config, feature_scaler)

    # 创建数据加载器（优化：添加多进程支持）
    num_workers = min(4, os.cpu_count() or 1)  # 使用多进程加速数据加载

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),  # GPU加速
        persistent_workers=True if num_workers > 0 else False,
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        persistent_workers=True if num_workers > 0 else False,
    )
    test_loader = DataLoader(
        test_dataset,
        batch_size=config.batch_size,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        persistent_workers=True if num_workers > 0 else False,
    )

    # 统计数据集中的正样本比例
    def count_positive_samples(dataset):
        if hasattr(dataset, 'labels'):
            labels = dataset.labels
            total_samples = len(labels)
            positive_samples = sum(1 for label in labels if label == 1)
            positive_ratio = positive_samples / total_samples if total_samples > 0 else 0
            return total_samples, positive_samples, positive_ratio
        return 0, 0, 0

    console.print(f"📊 数据集信息:")
    console.print(f"  训练集样本数: {len(train_dataset)}")
    console.print(f"  验证集样本数: {len(val_dataset)}")
    console.print(f"  测试集样本数: {len(test_dataset)}")

    # 统计正样本比例
    console.print(f"\n📈 数据集正样本统计:")
    for dataset, name in [(train_dataset, "训练集"), (val_dataset, "验证集"), (test_dataset, "测试集")]:
        total, positive, ratio = count_positive_samples(dataset)
        console.print(f"  {name}: {positive}/{total} ({ratio:.4f})")

    return train_loader, val_loader, test_loader, feature_scaler


def train_failure_prediction_model():
    """训练故障预测模型的主函数"""
    console.print(f"[bold green]🎯 故障预测模型训练 (PyTorch Lightning)[/bold green]")
    console.print("=" * 60)

    # 配置
    config = FailurePredictionConfig()

    # 初始化SwanLab实验跟踪
    if SWANLAB_AVAILABLE:
        # 准备完整的配置字典
        swanlab_config = {
            **config.get_model_info(),
            **config.get_training_info(),
            **config.get_data_info(),
            **config.get_loss_info(),
            "random_seed": config.random_seed,
            "accelerator": config.accelerator,
            "devices": config.devices,
        }

        # 初始化SwanLab
        swanlab.init(
            project="failure-prediction",
            experiment_name=f"{config.model_type}-{config.hidden_dim}d-{config.num_layers}l",
            description=f"故障预测模型训练 - {config.model_type.upper()}架构",
            config=swanlab_config
        )

        console.print(f"[green]✅ SwanLab实验跟踪已启动[/green]")
        console.print(f"   实验名称: {config.model_type}-{config.hidden_dim}d-{config.num_layers}l")
    else:
        console.print(f"[yellow]⚠️  SwanLab不可用，使用本地日志[/yellow]")

    # 检查GPU
    console.print(f"🖥️  使用设备: {config.accelerator}")
    if config.accelerator == "gpu":
        console.print(f"   GPU型号: {torch.cuda.get_device_name(0)}")
        console.print(
            f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB"
        )

    # 设置随机种子
    pl.seed_everything(config.random_seed)

    # 加载和划分数据
    train_data, val_data, test_data = load_and_split_data(config)

    # 创建数据加载器
    train_loader, val_loader, test_loader, feature_scaler = create_data_loaders(
        train_data, val_data, test_data, config
    )

    # 创建模型
    model = FailurePredictionModel(config)

    # 显示模型信息
    console.print(f"\n[bold blue]🏗️  模型信息:[/bold blue]")
    console.print(f"  架构: {config.model_type.upper()}")
    console.print(f"  特征维度: {config.feature_dim}")
    console.print(f"  隐藏维度: {config.hidden_dim}")
    console.print(f"  层数: {config.num_layers}")
    if config.model_type in ["lstm", "gru"]:
        console.print(f"  双向: {config.bidirectional}")
    elif config.model_type == "transformer":
        console.print(f"  注意力头数: {config.num_heads}")
    console.print(f"  最大序列长度: {config.max_seq_length}")
    console.print(f"  Dropout: {config.dropout_rate}")
    console.print(f"  时间去重: {config.enable_time_deduplication}")
    console.print(f"  预测模式: {config.use_prediction_mode}")
    if config.use_prediction_mode and config.filter_tp_before_hours is not None:
        console.print(f"  TP前过滤: {config.filter_tp_before_hours} 小时")

    # 显示数据平衡策略
    console.print(f"\n[bold cyan]⚖️  数据平衡策略:[/bold cyan]")
    if config.use_oversampling:
        console.print(f"  过采样: {config.oversampling_method}")
        console.print(f"  目标正样本比例: {config.oversampling_ratio}")
    elif config.use_class_weights:
        console.print(f"  类别权重: {config.pos_weight_scale}")
    else:
        console.print(f"  无特殊处理")

    # 显示损失函数信息
    console.print(f"\n[bold magenta]📉 损失函数:[/bold magenta]")
    if config.use_focal_loss:
        console.print(f"  Focal Loss (α={config.focal_alpha}, γ={config.focal_gamma})")
    elif config.use_label_smoothing:
        console.print(f"  标签平滑 BCE (smoothing={config.label_smoothing})")
    elif config.use_class_weights:
        console.print(f"  加权 BCE (pos_weight={config.pos_weight_scale})")
    else:
        console.print(f"  标准 BCE")

    # 更新SwanLab配置（添加模型参数数量）
    if SWANLAB_AVAILABLE:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        swanlab.config.update({
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "model_size_mb": total_params * 4 / (1024 * 1024),  # 假设float32
        })

        console.print(f"[green]✅ SwanLab配置已更新（模型参数: {total_params:,}）[/green]")

    # 创建回调
    callbacks = [
        EarlyStopping(
            monitor="val_f1",
            patience=config.patience,
            mode="max",
            verbose=True,
        ),
        ModelCheckpoint(
            dirpath=config.output_dir,
            filename="failure_prediction-{epoch:02d}-{val_f1:.3f}",
            monitor="val_f1",
            mode="max",
            save_top_k=3,
            verbose=True,
        ),
    ]

    # 添加SwanLab回调（如果可用）
    if SWANLAB_AVAILABLE:
        callbacks.append(SwanLabCallback())

    # 创建训练器
    trainer = pl.Trainer(
        accelerator=config.accelerator,
        devices=config.devices,
        max_epochs=config.max_epochs,
        callbacks=callbacks,
        enable_progress_bar=True,
        enable_model_summary=True,
        log_every_n_steps=50,
    )

    # 训练模型
    console.print("[bold blue]⚡ 开始训练...[/bold blue]")
    trainer.fit(model, train_loader, val_loader)

    # 测试模型
    console.print("[bold blue]🧪 开始测试...[/bold blue]")
    trainer.test(model, test_loader)

    # 保存模型
    model_path = os.path.join(
        config.output_dir,
        f"failure_prediction_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth",
    )
    torch.save(
        {
            "model_state_dict": model.state_dict(),
            "config": config.__dict__,
            "feature_scaler": feature_scaler,
            "trainer_state": trainer.state_dict() if hasattr(trainer, 'state_dict') else None,
        },
        model_path,
    )

    console.print(f"[green]💾 模型已保存到: {model_path}[/green]")
    console.print("[bold blue]🎉 模型训练完成！[/bold blue]")

    # SwanLab 结束
    if SWANLAB_AVAILABLE:
        try:
            swanlab.finish()
        except RuntimeError:
            # SwanLab 可能没有被初始化，跳过
            pass

    return model, trainer


if __name__ == "__main__":
    # 运行训练
    model, trainer = train_failure_prediction_model()
