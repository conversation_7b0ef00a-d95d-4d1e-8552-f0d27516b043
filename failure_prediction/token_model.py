#!/usr/bin/env python3
"""
基于Token-level embedding的故障预测模型
每个feature作为一个token，使用embedding处理分类特征
"""

import os
import gzip
import json
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
from typing import Dict, List, Tuple, Optional

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint

from torchmetrics.classification import (
    BinaryAccuracy,
    BinaryPrecision,
    BinaryRecall,
    BinaryF1Score,
    BinaryConfusionMatrix,
    BinaryAUROC,
)

from sklearn.preprocessing import StandardScaler

# SwanLab 实验跟踪
try:
    import swanlab
    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False

from rich.console import Console

console = Console()


class TokenEmbeddingConfig:
    """Token-level embedding配置"""
    
    def __init__(self):
        # 基础路径配置
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        
        self.data_path = os.path.join(
            project_root, "failure_prediction", "processed", 
            "processed_new_data_20250927_214625.json.gz"
        )
        self.output_dir = os.path.join(project_root, "failure_prediction", "models")
        self.logs_dir = os.path.join(project_root, "failure_prediction", "logs")
        
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)
        
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")
        
        self.random_seed = 42
        
        # 数据划分
        self.val_size = 0.15
        self.train_size = 0.7
        self.test_size = 1 - self.train_size - self.val_size
        
        # Token embedding参数
        self.max_seq_length = 50  # 最大序列长度
        self.min_seq_length = 2   # 最小序列长度
        self.embedding_dim = 64   # Token embedding维度
        
        # 字段词汇表大小（基于特征分析）
        self.vocab_sizes = {
            'exception_name': 300,   # field_1: 异常名称
            'exception_type': 100,   # field_2: 异常类型
            'warning_level': 10,     # field_3: 警告级别
            'tag_info': 10,          # field_4: 标签信息
        }
        
        # 时间位置编码
        self.use_time_encoding = True
        self.time_encoding_dim = 32
        self.max_time_minutes = 10080  # 7天
        
        # 模型架构
        self.model_type = "transformer"  # "transformer", "lstm", "gru"
        self.hidden_dim = 256
        self.num_layers = 4
        self.num_heads = 8
        self.dropout_rate = 0.3
        self.dim_feedforward = 512
        
        # 训练参数
        self.batch_size = 32
        self.learning_rate = 0.001
        self.max_epochs = 100
        self.patience = 15
        self.weight_decay = 1e-4
        
        # 数据平衡
        self.use_oversampling = True
        self.oversampling_ratio = 0.2  # 目标正样本比例
        
        # 损失函数
        self.use_focal_loss = True
        self.focal_alpha = 0.25
        self.focal_gamma = 2.0
        
        # 设备
        self.accelerator = "gpu" if torch.cuda.is_available() else "cpu"
        self.devices = [0] if torch.cuda.is_available() else "auto"


class TokenEmbeddingDataset(Dataset):
    """Token-level embedding数据集"""
    
    def __init__(self, data, config, vocab_mappings=None, is_training=True):
        self.data = data
        self.config = config
        self.is_training = is_training
        self.vocab_mappings = vocab_mappings
        
        # 构建词汇表（仅训练集）
        if self.is_training and not self.vocab_mappings:
            self._build_vocabularies()
        
        # 预处理数据
        self._preprocess_data()
        
        # 过采样（仅训练集）
        if self.is_training and config.use_oversampling:
            self._apply_oversampling()
    
    def _build_vocabularies(self):
        """构建词汇表"""
        console.print("[cyan]🔧 构建Token词汇表...[/cyan]")
        
        field_values = defaultdict(set)
        
        # 收集所有字段值
        for item in self.data[:20000]:  # 使用前2万样本构建词汇表
            feature_list = item.get("feature_list", [])
            for feature in feature_list:
                if len(feature) >= 5:
                    field_values['exception_name'].add(str(feature[1]))
                    field_values['exception_type'].add(str(feature[2]))
                    field_values['warning_level'].add(str(feature[3]))
                    field_values['tag_info'].add(str(feature[4]))
        
        # 构建映射（0=PAD, 1=UNK）
        self.vocab_mappings = {}
        for field_name, values in field_values.items():
            vocab = {'<PAD>': 0, '<UNK>': 1}
            for i, value in enumerate(sorted(values), 2):
                if i < self.config.vocab_sizes[field_name]:
                    vocab[value] = i
            self.vocab_mappings[field_name] = vocab
            console.print(f"   - {field_name}: {len(vocab)} tokens")
    
    def _parse_timestamp(self, timestamp_str):
        """解析时间戳"""
        formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue
        return None
    
    def _preprocess_data(self):
        """预处理数据"""
        console.print(f"[cyan]🔄 Token-level数据预处理...[/cyan]")
        
        token_sequences = []
        time_positions = []
        labels = []
        lengths = []
        
        for item_idx, item in enumerate(self.data):
            if item_idx % 10000 == 0 and item_idx > 0:
                console.print(f"[cyan]已处理 {item_idx:,} 样本...[/cyan]")
            
            try:
                feature_list = item.get("feature_list", [])
                if len(feature_list) < self.config.min_seq_length:
                    continue
                
                # 解析时间戳并排序
                timestamped_features = []
                for feature in feature_list:
                    if len(feature) >= 5:
                        timestamp = self._parse_timestamp(str(feature[0]))
                        if timestamp:
                            timestamped_features.append((timestamp, feature))
                
                if len(timestamped_features) < self.config.min_seq_length:
                    continue
                
                # 按时间排序
                timestamped_features.sort(key=lambda x: x[0])
                
                # 限制序列长度
                timestamped_features = timestamped_features[:self.config.max_seq_length]
                
                # 提取tokens和时间位置
                tokens = []
                time_pos = []
                base_time = timestamped_features[0][0]
                
                for timestamp, feature in timestamped_features:
                    # 转换为token IDs
                    token_ids = [
                        self.vocab_mappings['exception_name'].get(str(feature[1]), 1),
                        self.vocab_mappings['exception_type'].get(str(feature[2]), 1),
                        self.vocab_mappings['warning_level'].get(str(feature[3]), 1),
                        self.vocab_mappings['tag_info'].get(str(feature[4]), 1),
                    ]
                    tokens.append(token_ids)
                    
                    # 时间位置（分钟）
                    time_diff = (timestamp - base_time).total_seconds() / 60.0
                    time_pos.append(min(int(time_diff), self.config.max_time_minutes - 1))
                
                # 处理标签
                label_list = item.get("label_list", [])
                if label_list:
                    label = 1 if label_list[0] <= 4 else 0
                    
                    token_sequences.append(tokens)
                    time_positions.append(time_pos)
                    labels.append(label)
                    lengths.append(len(tokens))
                
            except Exception as e:
                continue
        
        self.token_sequences = token_sequences
        self.time_positions = time_positions
        self.labels = np.array(labels, dtype=np.int32)
        self.lengths = np.array(lengths, dtype=np.int32)
        
        console.print(f"✅ 预处理完成！有效样本: {len(token_sequences):,}")
        
        # 统计标签分布
        unique, counts = np.unique(self.labels, return_counts=True)
        label_dist = dict(zip(unique, counts))
        console.print(f"📊 标签分布: {label_dist}")
    
    def _apply_oversampling(self):
        """应用过采样"""
        console.print("[cyan]🔄 应用过采样...[/cyan]")
        
        pos_indices = np.where(self.labels == 1)[0]
        neg_indices = np.where(self.labels == 0)[0]
        
        target_pos_count = int(len(neg_indices) * self.config.oversampling_ratio / (1 - self.config.oversampling_ratio))
        
        if target_pos_count > len(pos_indices):
            # 需要过采样
            samples_to_add = target_pos_count - len(pos_indices)
            np.random.seed(self.config.random_seed)
            indices_to_duplicate = np.random.choice(pos_indices, size=samples_to_add, replace=True)
            
            # 复制样本
            for idx in indices_to_duplicate:
                self.token_sequences.append(self.token_sequences[idx].copy())
                self.time_positions.append(self.time_positions[idx].copy())
                self.lengths = np.append(self.lengths, self.lengths[idx])
            
            # 添加标签
            new_labels = np.ones(samples_to_add, dtype=np.int32)
            self.labels = np.concatenate([self.labels, new_labels])
            
            console.print(f"✅ 过采样完成！数据集大小: {len(self.token_sequences):,}")
    
    def __len__(self):
        return len(self.token_sequences)
    
    def __getitem__(self, idx):
        tokens = torch.LongTensor(self.token_sequences[idx])
        time_pos = torch.LongTensor(self.time_positions[idx])
        label = torch.FloatTensor([self.labels[idx]]).squeeze()
        length = torch.LongTensor([self.lengths[idx]])
        
        return {
            "tokens": tokens,
            "time_positions": time_pos,
            "label": label,
            "length": length,
        }
